#!/usr/bin/env python3
import base64
import json
import os
from typing import Any, Dict, Optional
import requests

# Wallet RPC client (separate from daemon). Enable --rpc-login or cookie auth.

class WalletRPC:
    def __init__(self, endpoint: str, username: Optional[str] = None, password: Optional[str] = None,
                 use_tor_socks: bool = False, timeout: float = 20.0):
        self.endpoint = endpoint.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        if username and password:
            token = base64.b64encode(f"{username}:{password}".encode()).decode()
            self.session.headers.update({"Authorization": f"Basic {token}"})
        if use_tor_socks:
            self.session.proxies.update({
                "http": "socks5h://127.0.0.1:9050",
                "https": "socks5h://127.0.0.1:9050",
            })

    def call(self, method: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        payload = {"jsonrpc": "2.0", "id": "0", "method": method, "params": params or {}}
        r = self.session.post(f"{self.endpoint}/json_rpc", json=payload, timeout=self.timeout)
        r.raise_for_status()
        return r.json()

    # Convenience
    def create_wallet(self, filename: str, password: str):
        return self.call("create_wallet", {"filename": filename, "password": password, "language": "English"})

    def open_wallet(self, filename: str, password: str):
        return self.call("open_wallet", {"filename": filename, "password": password})

    def get_address(self, account_index: int = 0):
        return self.call("get_address", {"account_index": account_index})

    def get_balance(self, account_index: int = 0):
        return self.call("get_balance", {"account_index": account_index})

    def transfer(self, destinations, account_index: int = 0, ring_size: int = 11):
        return self.call("transfer", {
            "destinations": destinations,
            "account_index": account_index,
            "ring_size": ring_size,
            "priority": 2
        })

    def get_transfer_by_txid(self, txid: str, account_index: int = 0):
        return self.call("get_transfer_by_txid", {
            "txid": txid,
            "account_index": account_index
        })

if __name__ == "__main__":
    endpoint = os.environ.get("WALLET_RPC", "http://127.0.0.1:18083")
    username = os.environ.get("RPC_USER")
    password = os.environ.get("RPC_PASS")
    w = WalletRPC(endpoint, username, password)
    try:
        print(json.dumps(w.get_address(), indent=2)[:1000])
    except Exception as e:
        print(f"WalletRPC error: {e}")

