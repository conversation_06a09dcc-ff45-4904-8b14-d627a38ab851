#!/usr/bin/env python3
import base64
import json
import os
import time
import pytest
from datetime import datetime, timedelta, timezone

from layer3.protocols.ap2 import build_ap2_auth, verify_ap2_auth, sign_mandate, verify_mandate, verify_imported_mandate

pynacl = pytest.importorskip("nacl")
from nacl import signing  # type: ignore


def test_ap2_auth_and_mandate_verification_roundtrip():
    sk = signing.SigningKey.generate()
    pk_b64 = base64.b64encode(sk.verify_key.encode()).decode()

    # Build a mandate and sign
    mandate = {
        "mandate_id": "m1",
        "payer_id": pk_b64,
        "payee_id": "service-xyz",
        "asset": "AN",
        "spending_cap": "100000",
        "valid_from": "2025-01-01T00:00:00Z",
        "valid_until": "2026-01-01T00:00:00Z",
        "allowed_endpoints": ["/service"],
        "terms_cid": "",
    }
    sig_b64 = sign_mandate(mandate, sk.encode())
    assert verify_mandate(mandate, sig_b64, sk.verify_key.encode())

    # Build AP2 auth
    invoice_id = "inv-123"
    nonce = "abc123"
    auth = build_ap2_auth(invoice_id, nonce, mandate["mandate_id"], sk.encode())
    assert verify_ap2_auth(auth, invoice_id, nonce, pk_b64)


def test_verify_imported_mandate_success():
    sk = signing.SigningKey.generate()
    pk_b64 = base64.b64encode(sk.verify_key.encode()).decode()
    current_time = datetime.utcnow().replace(tzinfo=timezone.utc)
    valid_from = (current_time - timedelta(minutes=5)).isoformat(timespec='seconds').replace('+00:00', 'Z')
    valid_until = (current_time + timedelta(minutes=5)).isoformat(timespec='seconds').replace('+00:00', 'Z')

    mandate = {
        "mandate_id": "m1",
        "payer_id": pk_b64,
        "payee_id": "service-xyz",
        "asset": "AN",
        "spending_cap": "100000",
        "valid_from": valid_from,
        "valid_until": valid_until,
        "allowed_endpoints": ["/service"],
        "terms_cid": "",
    }
    mandate_sig = sign_mandate(mandate, sk.encode())
    mandate["payer_signature"] = mandate_sig

    assert verify_imported_mandate(mandate, "/service")


def test_verify_imported_mandate_invalid_signature():
    sk = signing.SigningKey.generate()
    pk_b64 = base64.b64encode(sk.verify_key.encode()).decode()
    current_time = datetime.utcnow().replace(tzinfo=timezone.utc)
    valid_from = (current_time - timedelta(minutes=5)).isoformat(timespec='seconds').replace('+00:00', 'Z')
    valid_until = (current_time + timedelta(minutes=5)).isoformat(timespec='seconds').replace('+00:00', 'Z')

    mandate = {
        "mandate_id": "m1",
        "payer_id": pk_b64,
        "payee_id": "service-xyz",
        "asset": "AN",
        "spending_cap": "100000",
        "valid_from": valid_from,
        "valid_until": valid_until,
        "allowed_endpoints": ["/service"],
        "terms_cid": "",
    }
    mandate_sig = sign_mandate(mandate, sk.encode())
    mandate["payer_signature"] = mandate_sig + "tampered" # Tamper the signature

    assert not verify_imported_mandate(mandate, "/service")


def test_verify_imported_mandate_expired():
    sk = signing.SigningKey.generate()
    pk_b64 = base64.b64encode(sk.verify_key.encode()).decode()
    current_time = datetime.utcnow().replace(tzinfo=timezone.utc)
    valid_from = (current_time - timedelta(minutes=10)).isoformat(timespec='seconds').replace('+00:00', 'Z')
    valid_until = (current_time - timedelta(minutes=5)).isoformat(timespec='seconds').replace('+00:00', 'Z') # Expired

    mandate = {
        "mandate_id": "m1",
        "payer_id": pk_b64,
        "payee_id": "service-xyz",
        "asset": "AN",
        "spending_cap": "100000",
        "valid_from": valid_from,
        "valid_until": valid_until,
        "allowed_endpoints": ["/service"],
        "terms_cid": "",
    }
    mandate_sig = sign_mandate(mandate, sk.encode())
    mandate["payer_signature"] = mandate_sig

    assert not verify_imported_mandate(mandate, "/service")


def test_verify_imported_mandate_not_yet_valid():
    sk = signing.SigningKey.generate()
    pk_b64 = base64.b64encode(sk.verify_key.encode()).decode()
    current_time = datetime.utcnow().replace(tzinfo=timezone.utc)
    valid_from = (current_time + timedelta(minutes=5)).isoformat(timespec='seconds').replace('+00:00', 'Z') # Not yet valid
    valid_until = (current_time + timedelta(minutes=10)).isoformat(timespec='seconds').replace('+00:00', 'Z')

    mandate = {
        "mandate_id": "m1",
        "payer_id": pk_b64,
        "payee_id": "service-xyz",
        "asset": "AN",
        "spending_cap": "100000",
        "valid_from": valid_from,
        "valid_until": valid_until,
        "allowed_endpoints": ["/service"],
        "terms_cid": "",
    }
    mandate_sig = sign_mandate(mandate, sk.encode())
    mandate["payer_signature"] = mandate_sig

    assert not verify_imported_mandate(mandate, "/service")


def test_verify_imported_mandate_wrong_endpoint():
    sk = signing.SigningKey.generate()
    pk_b64 = base64.b64encode(sk.verify_key.encode()).decode()
    current_time = datetime.utcnow().replace(tzinfo=timezone.utc)
    valid_from = (current_time - timedelta(minutes=5)).isoformat(timespec='seconds').replace('+00:00', 'Z')
    valid_until = (current_time + timedelta(minutes=5)).isoformat(timespec='seconds').replace('+00:00', 'Z')

    mandate = {
        "mandate_id": "m1",
        "payer_id": pk_b64,
        "payee_id": "service-xyz",
        "asset": "AN",
        "spending_cap": "100000",
        "valid_from": valid_from,
        "valid_until": valid_until,
        "allowed_endpoints": ["/service"],
        "terms_cid": "",
    }
    mandate_sig = sign_mandate(mandate, sk.encode())
    mandate["payer_signature"] = mandate_sig

    assert not verify_imported_mandate(mandate, "/wrong-service")


def test_verify_ap2_auth_freshness_fail():
    sk = signing.SigningKey.generate()
    pk_b64 = base64.b64encode(sk.verify_key.encode()).decode()

    invoice_id = "inv-123"
    nonce = "abc123"
    mandate_id = "m1"

    # Manually build auth with an old timestamp
    old_timestamp = int(time.time()) - 600 # 10 minutes ago
    payload = {"invoice_id": invoice_id, "nonce": nonce, "mandate_id": mandate_id, "ts": old_timestamp}
    msg = json.dumps(payload, separators=(',', ':'), sort_keys=True).encode()
    sig = base64.b64encode(sk.sign(msg).signature).decode()
    payload["sig"] = sig
    auth = base64.b64encode(json.dumps(payload).encode()).decode()

    assert not verify_ap2_auth(auth, invoice_id, nonce, pk_b64)


def test_verify_ap2_auth_invoice_nonce_binding_fail():
    sk = signing.SigningKey.generate()
    pk_b64 = base64.b64encode(sk.verify_key.encode()).decode()

    invoice_id = "inv-123"
    nonce = "abc123"
    mandate_id = "m1"

    auth = build_ap2_auth(invoice_id, nonce, mandate_id, sk.encode())

    # Test with wrong invoice_id
    assert not verify_ap2_auth(auth, "wrong-invoice", nonce, pk_b64)
    # Test with wrong nonce
    assert not verify_ap2_auth(auth, invoice_id, "wrong-nonce", pk_b64)

