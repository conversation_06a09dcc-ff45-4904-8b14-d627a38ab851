#!/usr/bin/env python3
import os
from layer3.agents.agent_runtime import get_planner


def test_planner_prefers_ap2_when_flag_set(monkeypatch):
    monkeypatch.setenv("AGENTNET_USE_AP2", "1")
    p = get_planner()
    decision = p.decide_payment({"ap2_supported": True})
    assert decision == "ap2"


def test_planner_falls_back_onchain_when_flag_missing(monkeypatch):
    monkeypatch.delenv("AGENTNET_USE_AP2", raising=False)
    p = get_planner()
    decision = p.decide_payment({"ap2_supported": True})
    assert decision == "onchain"

