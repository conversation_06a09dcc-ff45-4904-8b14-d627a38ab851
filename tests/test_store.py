#!/usr/bin/env python3
import base64
import json
import os
import tempfile

import pytest

from layer3.state.store import JSONStore
from layer3.protocols.ap2 import sign_mandate

pynacl = pytest.importorskip("nacl")
from nacl import signing  # type: ignore


def test_store_import_and_consume(tmp_path):
    sk = signing.SigningKey.generate()
    pk_b64 = base64.b64encode(sk.verify_key.encode()).decode()

    mandate = {
        "mandate_id": "m1",
        "payer_id": pk_b64,
        "payee_id": "service-xyz",
        "asset": "AN",
        "spending_cap": "1000",
        "valid_from": "2025-01-01T00:00:00Z",
        "valid_until": "2026-01-01T00:00:00Z",
        "allowed_endpoints": ["/service"],
        "terms_cid": "",
    }
    mandate["payer_signature"] = sign_mandate(mandate, sk.encode())

    mfile = tmp_path / "m.json"
    with open(mfile, "w") as f:
        json.dump([mandate], f)

    os.environ["AGENTNET_STATE_FILE"] = str(tmp_path / "state.json")
    s = JSONStore()
    s.import_mandates_file(str(mfile))

    assert s.get_mandate("m1") is not None
    assert s.consume("m1", 500)
    assert not s.consume("m1", 600)  # exceeds cap

