#!/bin/bash

# Function to check if Tor is installed
is_tor_installed() {
    dpkg -s tor &> /dev/null
}

# Install Tor if not already present
if ! is_tor_installed; then
    echo "Tor is not installed. Installing Tor..."
    sudo apt update
    sudo apt install -y tor
else
    echo "Tor is already installed."
fi

# Define Hidden Service directory
HIDDEN_SERVICE_DIR="/var/lib/tor/hidden_service/"

# Create Hidden Service directory and set permissions
echo "Creating Hidden Service directory: $HIDDEN_SERVICE_DIR"
sudo mkdir -p "$HIDDEN_SERVICE_DIR"
sudo chown -R debian-tor:debian-tor "$HIDDEN_SERVICE_DIR"
sudo chmod -R 700 "$HIDDEN_SERVICE_DIR"

# Copy torrc configuration
echo "Copying torrc.hidden_service to /etc/tor/torrc"
sudo cp layer1/counter_router/torrc.hidden_service /etc/tor/torrc

# Restart Tor service to apply changes
echo "Restarting Tor service..."
sudo systemctl restart tor

echo "Tor Hidden Service setup complete."
echo "To get your .onion address, run: sudo cat /var/lib/tor/hidden_service/hostname"