import socket
import socks
import stem.process
from stem.util import term

# Configuration for the Tor SOCKS proxy
SOCKS_PORT = 9050  # Default Tor SOCKS port
TOR_PROXY_ADDRESS = '127.0.0.1'

# Placeholder for the Hidden Service address and port
HIDDEN_SERVICE_ADDRESS = "example.onion"  # Replace with the actual .onion address
HIDDEN_SERVICE_PORT = 80  # Replace with the actual port

def start_tor_process():
    """
    Starts a new Tor process and returns it.
    This is a basic example; in a real application, you might want to manage
    the Tor process more robustly (e.g., check if one is already running).
    """
    print(term.format("Starting Tor process...", term.Color.BLUE))
    try:
        # You might need to specify the path to your tor executable
        # if it's not in your system's PATH.
        # For example: tor_path="/usr/bin/tor"
        tor_process = stem.process.launch_tor_with_config(
            config={
                'SocksPort': str(SOCKS_PORT),
                'ControlPort': '9051',
            },
            init_msg_handler=lambda line: print(term.format(line, term.Color.GREEN)),
        )
        print(term.format("Tor process started.", term.Color.GREEN))
        return tor_process
    except Exception as e:
        print(term.format(f"Failed to start Tor process: {e}", term.Color.RED))
        return None

def create_tor_socket():
    """
    Creates a socket configured to use the Tor SOCKS proxy.
    """
    print(term.format(f"Configuring socket to use Tor SOCKS proxy at {TOR_PROXY_ADDRESS}:{SOCKS_PORT}...", term.Color.BLUE))
    socks.set_default_proxy(socks.SOCKS5, TOR_PROXY_ADDRESS, SOCKS_PORT)
    socket.socket = socks.socksocket
    print(term.format("Socket configured for Tor.", term.Color.GREEN))
    return socket.socket()

def main():
    tor_process = None
    try:
        # Start Tor process (optional, if Tor is not already running)
        # tor_process = start_tor_process()
        # if tor_process is None:
        #     print(term.format("Exiting because Tor process could not be started.", term.Color.RED))
        #     return

        # Create a Tor-enabled socket
        client_socket = create_tor_socket()

        print(term.format(f"Attempting to connect to Hidden Service: {HIDDEN_SERVICE_ADDRESS}:{HIDDEN_SERVICE_PORT}...", term.Color.BLUE))
        client_socket.connect((HIDDEN_SERVICE_ADDRESS, HIDDEN_SERVICE_PORT))
        print(term.format("Successfully connected to Hidden Service.", term.Color.GREEN))

        # Send a message
        message_to_send = "Hello from Tor client!"
        client_socket.sendall(message_to_send.encode('utf-8'))
        print(term.format(f"Sent: '{message_to_send}'", term.Color.YELLOW))

        # Receive a response
        response = client_socket.recv(1024).decode('utf-8')
        print(term.format(f"Received: '{response}'", term.Color.CYAN))

    except socks.ProxyConnectionError as e:
        print(term.format(f"SOCKS proxy connection error: {e}. Ensure Tor is running and accessible on {TOR_PROXY_ADDRESS}:{SOCKS_PORT}.", term.Color.RED))
    except socket.error as e:
        print(term.format(f"Socket error: {e}. Could not connect to the Hidden Service. Check the .onion address and port.", term.Color.RED))
    except Exception as e:
        print(term.format(f"An unexpected error occurred: {e}", term.Color.RED))
    finally:
        if 'client_socket' in locals() and client_socket:
            client_socket.close()
            print(term.format("Client socket closed.", term.Color.BLUE))
        if tor_process:
            print(term.format("Stopping Tor process...", term.Color.BLUE))
            tor_process.kill()
            print(term.format("Tor process stopped.", term.Color.GREEN))

if __name__ == "__main__":
    main()