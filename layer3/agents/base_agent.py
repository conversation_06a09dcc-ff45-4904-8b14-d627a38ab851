#!/usr/bin/env python3
from typing import Dict, Any, Optional
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from .identity_manager import AgentIdentityManager
from .decision_maker import DecisionMaker

class AgentState:
    def __init__(self):
        self.messages: list = []
        self.current_task: Optional[str] = None
        self.context: Dict[str, Any] = {}
        self.response: Optional[str] = None

class BaseAgent:
    def __init__(self, agent_id: str, identity_manager: AgentIdentityManager):
        self.agent_id = agent_id
        self.identity_manager = identity_manager
        self.decision_maker = DecisionMaker()
        self.state = AgentState()

        # Build the LangGraph
        self.graph = self._build_graph()

    def _build_graph(self) -> StateGraph:
        """Build the agent's decision graph."""
        graph = StateGraph(AgentState)

        # Add nodes
        graph.add_node("receive_message", self._receive_message)
        graph.add_node("process_message", self._process_message)
        graph.add_node("decide_action", self._decide_action)
        graph.add_node("execute_action", self._execute_action)
        graph.add_node("send_response", self._send_response)

        # Add edges
        graph.add_edge(START, "receive_message")
        graph.add_edge("receive_message", "process_message")
        graph.add_edge("process_message", "decide_action")
        graph.add_edge("decide_action", "execute_action")
        graph.add_edge("execute_action", "send_response")
        graph.add_edge("send_response", END)

        # Add conditional edges if needed
        # graph.add_conditional_edges("decide_action", self._route_decision, {"respond": "send_response", "delegate": "delegate_task"})

        return graph.compile(checkpointer=MemorySaver())

    def _receive_message(self, state: AgentState) -> AgentState:
        """Node: Receive and store incoming message."""
        # In a real implementation, this would handle incoming A2A messages
        if state.messages:
            state.current_task = state.messages[-1].get('body', {}).get('task')
        return state

    def _process_message(self, state: AgentState) -> AgentState:
        """Node: Process the message content."""
        # Basic processing - extract intent, validate, etc.
        if state.current_task:
            state.context['processed'] = True
        return state

    def _decide_action(self, state: AgentState) -> AgentState:
        """Node: Decide what action to take."""
        if state.messages:
            last_msg = state.messages[-1]
            msg_type = last_msg.get('type', 'unknown')
            decision = self.decision_maker.make_decision(msg_type, last_msg.get('body', {}))
            state.context['action'] = decision
        else:
            state.context['action'] = 'idle'
        return state

    def _execute_action(self, state: AgentState) -> AgentState:
        """Node: Execute the decided action."""
        action = state.context.get('action')
        if action == 'execute':
            # Placeholder for actual execution
            state.response = f"Executed task: {state.current_task}"
        return state

    def _send_response(self, state: AgentState) -> AgentState:
        """Node: Send response back."""
        # In real implementation, send via A2A
        return state

    def handle_message(self, message: Dict[str, Any]) -> Optional[str]:
        """Handle an incoming message through the graph."""
        self.state.messages.append(message)
        config = {"configurable": {"thread_id": self.agent_id}}
        result = self.graph.invoke(self.state, config)
        return result.response

    def get_identity(self) -> Optional[Dict]:
        """Get the agent's identity."""
        return self.identity_manager.get_identity(self.agent_id)