# Agent Economy Layer - Agent Framework

This directory contains the basic agent framework for the Agent Economy Layer, implementing autonomous agents that can communicate via A2A protocol over Tor.

## Components

### Core Modules

- `base_agent.py`: Foundational agent class using LangGraph for workflow management
- `identity_manager.py`: Agent identity management with JSON schema validation
- `lifecycle_manager.py`: Manages agent registration, starting, and stopping
- `message_handler.py`: Handles incoming and outgoing A2A messages
- `decision_maker.py`: Basic decision-making logic for agents

### Protocols

- `a2a.py`: Agent-to-Agent communication protocol with encryption and signatures

## Features

- **Identity Management**: Agents have self-certifying identities validated against JSON schemas
- **A2A Communication**: Secure inter-agent messaging with optional encryption
- **Tor Integration**: All communication routed through Tor for anonymity
- **LangGraph Workflows**: Agents use graph-based workflows for decision making
- **Lifecycle Management**: Centralized control of agent lifecycles

## Usage

```python
from layer3.agents.identity_manager import AgentIdentityManager
from layer3.agents.base_agent import BaseAgent
from layer3.agents.lifecycle_manager import AgentLifecycleManager

# Create identity manager
im = AgentIdentityManager()

# Generate agent identity
identity = im.generate_identity()

# Create agent
agent = BaseAgent(identity['id'], im)

# Register with lifecycle manager
lm = AgentLifecycleManager(im)
lm.register_agent(agent)
lm.start_agent(identity['id'])
```

## Testing

Run tests with:
```bash
cd layer3/agents/tests
PYTHONPATH=../../.. python -m pytest .
```

## Security Notes

- All agent identities are validated against schemas
- Messages can be encrypted and signed
- Communication uses Tor for anonymity
- No full payment/crypto implementation yet