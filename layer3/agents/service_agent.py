#!/usr/bin/env python3
import json
import os
import random
import time
import uuid
from datetime import datetime

from http.server import <PERSON><PERSON><PERSON><PERSON>equestHand<PERSON>, HTTPServer
from urllib.parse import urlparse

from layer3.protocols.x402_middleware import X402
from layer2.chain.wallet_rpc_client import WalletRPC
from layer3.protocols.ap2 import verify_ap2_auth
from layer3.state.store import JSONStore

ASSET = os.environ.get('AGENTNET_ASSET', 'AN')
PRICE = int(os.environ.get('AGENTNET_PRICE_ATOMIC', '50000'))
ADDRESS = os.environ.get('AGENTNET_ADDRESS', 'agentnet_demo_address')
HOST = '127.0.0.1'
PORT = int(os.environ.get('AGENTNET_SERVICE_PORT', '8090'))

# Wallet RPC for tx verification
WALLET_RPC = os.environ.get('WALLET_RPC', 'http://127.0.0.1:18083')
RPC_USER = os.environ.get('RPC_USER')
RPC_PASS = os.environ.get('RPC_PASS')

# Mandate store (optional)
MANDATES_FILE = os.environ.get('AGENTNET_MANDATES_FILE')
PAYER_PUBKEY_B64 = os.environ.get('AGENTNET_PAYER_PUBKEY_B64')  # fallback trust if mandate not loaded

x402 = X402(ASSET, PRICE, ADDRESS)
wallet = WalletRPC(WALLET_RPC, RPC_USER, RPC_PASS, use_tor_socks=True)
store = JSONStore()
if MANDATES_FILE and os.path.exists(MANDATES_FILE):
    try:
        store.import_mandates_file(MANDATES_FILE)
    except Exception:
        pass


class Handler(BaseHTTPRequestHandler):
    server_version = "AgentService/1.0"

    def _send(self, code: int, obj):
        body = json.dumps(obj).encode()
        self.send_response(code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(body)))
        self.end_headers()
        self.wfile.write(body)

    def do_GET(self):  # noqa: N802
        time.sleep(random.uniform(0.02, 0.12))
        p = urlparse(self.path)
        if p.path == '/health':
            return self._send(200, {"ok": True})
        if p.path == '/service':
            # 1) Check for proof headers
            headers = {k: v for k, v in self.headers.items()}
            proof = x402.check_payment(headers)
            if not proof:
                # Issue 402 challenge and record invoice in-memory (nonce binding)
                invoice_id = str(uuid.uuid4())
                nonce = uuid.uuid4().hex
                # Record minimal state for AP2 verification (nonce + invoice_id)
                self.server.__dict__.setdefault('invoices', {})[invoice_id] = {
                    'nonce': nonce,
                    'amount': PRICE,
                    'ts': int(time.time()),
                }
                code, body = x402.challenge(invoice_id, nonce, ap2_supported=True)
                return self._send(code, body)
            # 2) Verify on-chain proof
            if headers.get('X-402-TxId'):
                txid = headers['X-402-TxId']
                try:
                    info = wallet.get_transfer_by_txid(txid)
                    tr = info.get('result', {}).get('transfer', {})
                    # Basic checks: incoming, amount >= price
                    if tr.get('type') != 'in' or int(tr.get('amount', 0)) < PRICE:
                        return self._send(402, {"error": "insufficient or invalid payment"})
                except Exception as e:
                    return self._send(402, {"error": f"tx verification failed: {e}"})
                payload = {"result": "service-response", "invoice_proof": txid}
                return self._send(200, payload)
            # 3) Verify AP2 mandate path
            if headers.get('X-AP2-Auth'):
                auth_b64 = headers['X-AP2-Auth']
                # Retrieve invoice context
                invoices = self.server.__dict__.get('invoices', {})
                inv_id, inv = (None, None)
                if invoices:
                    inv_id = sorted(invoices.items(), key=lambda kv: kv[1]['ts'], reverse=True)[0][0]
                    inv = invoices[inv_id]
                if not inv:
                    return self._send(402, {"error": "no invoice context"})
                # Decode payload to extract mandate_id for lookup and time freshness
                import base64, json as _json
                try:
                    payload = _json.loads(base64.b64decode(auth_b64).decode())
                except Exception:
                    return self._send(402, {"error": "malformed AP2 payload"})
                # Freshness: 5 minutes max age
                if abs(int(time.time()) - int(payload.get('ts', 0))) > 300:
                    return self._send(402, {"error": "stale AP2 authorization"})
                mandate_id = payload.get('mandate_id')
                m = store.get_mandate(mandate_id) if mandate_id else None
                if not m:
                    return self._send(402, {"error": "unknown mandate"})
                # Verify AP2 signature against mandate's payer public key
                payer_pk_b64 = m.get('payer_id', '')
                if not verify_ap2_auth(auth_b64, inv_id, inv['nonce'], payer_pk_b64):
                    return self._send(402, {"error": "invalid AP2 authorization"})
                # Enforce endpoint scope and validity window
                try:
                    if p.path not in set(m.get('allowed_endpoints', [])):
                        return self._send(402, {"error": "endpoint not allowed by mandate"})
                    fmt = '%Y-%m-%dT%H:%M:%SZ'
                    now = int(time.time())
                    vf = int(datetime.strptime(m['valid_from'], fmt).timestamp())
                    vu = int(datetime.strptime(m['valid_until'], fmt).timestamp())
                    if not (vf <= now <= vu):
                        return self._send(402, {"error": "mandate not currently valid"})
                except Exception:
                    return self._send(402, {"error": "invalid mandate validity fields"})
                # Consume from mandate cap
                if not store.consume(mandate_id, PRICE):
                    return self._send(402, {"error": "mandate cap exceeded or revoked"})
                return self._send(200, {"result": "service-response", "ap2": {"mandate_id": mandate_id}})
            return self._send(402, {"error": "missing proof"})
        return self._send(404, {"error": "not found"})

    def log_message(self, fmt, *args):
        pass


if __name__ == '__main__':
    httpd = HTTPServer((HOST, PORT), Handler)
    print(f"Service agent on http://{HOST}:{PORT} (behind Tor HS recommended)")
    httpd.serve_forever()

