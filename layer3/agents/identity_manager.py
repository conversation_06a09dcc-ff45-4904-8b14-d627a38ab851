#!/usr/bin/env python3
import json
import os
import base64
import hashlib
from datetime import datetime
from typing import Dict, Optional
import jsonschema
from nacl import signing, public

# Load the agent identity schema
SCHEMA_PATH = os.path.join(os.path.dirname(__file__), '../../docs/schemas/agent_identity.schema.json')
with open(SCHEMA_PATH, 'r') as f:
    AGENT_IDENTITY_SCHEMA = json.load(f)

class AgentIdentityManager:
    def __init__(self):
        self.identities: Dict[str, Dict] = {}

    def generate_identity(self, encryption_key: Optional[bytes] = None,
                         signature_key: Optional[bytes] = None,
                         payment_address: str = "") -> Dict:
        """Generate a new agent identity with keys if not provided."""
        if not signature_key:
            signing_key = signing.SigningKey.generate()
            signature_key = signing_key.encode()
        else:
            signing_key = signing.SigningKey(signature_key)

        if not encryption_key:
            encryption_key = public.PrivateKey.generate().encode()

        # Create self-certifying ID from signature public key
        pubkey_bytes = signing_key.verify_key.encode()
        id_hash = hashlib.sha256(pubkey_bytes).digest()[:32]
        agent_id = base64.urlsafe_b64encode(id_hash).decode().rstrip('=')

        identity = {
            "id": agent_id,
            "encryption_public_key": base64.b64encode(public.PrivateKey(encryption_key).public_key.encode()).decode(),
            "signature_public_key": base64.b64encode(pubkey_bytes).decode(),
            "payment_address": payment_address,
            "created_at": datetime.utcnow().isoformat() + "Z"
        }

        # Validate against schema
        jsonschema.validate(instance=identity, schema=AGENT_IDENTITY_SCHEMA)

        self.identities[agent_id] = identity
        return identity

    def load_identity(self, identity_data: Dict) -> bool:
        """Load and validate an existing identity."""
        try:
            jsonschema.validate(instance=identity_data, schema=AGENT_IDENTITY_SCHEMA)
            self.identities[identity_data['id']] = identity_data
            return True
        except jsonschema.ValidationError:
            return False

    def get_identity(self, agent_id: str) -> Optional[Dict]:
        """Retrieve an identity by ID."""
        return self.identities.get(agent_id)

    def validate_identity(self, identity: Dict) -> bool:
        """Validate an identity against the schema."""
        try:
            jsonschema.validate(instance=identity, schema=AGENT_IDENTITY_SCHEMA)
            return True
        except jsonschema.ValidationError:
            return False

    def sign_message(self, agent_id: str, message: bytes, signing_key: bytes) -> Optional[str]:
        """Sign a message with the agent's signing key."""
        identity = self.get_identity(agent_id)
        if not identity:
            return None

        sk = signing.SigningKey(signing_key)
        signature = sk.sign(message).signature
        return base64.b64encode(signature).decode()

    def verify_signature(self, agent_id: str, message: bytes, signature_b64: str) -> bool:
        """Verify a signature for an agent."""
        identity = self.get_identity(agent_id)
        if not identity:
            return False

        try:
            vk = signing.VerifyKey(base64.b64decode(identity['signature_public_key']))
            vk.verify(message, base64.b64decode(signature_b64))
            return True
        except Exception:
            return False