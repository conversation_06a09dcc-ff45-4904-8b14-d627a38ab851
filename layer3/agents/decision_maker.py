#!/usr/bin/env python3
from typing import Dict, Any, Optional

class DecisionMaker:
    def __init__(self):
        self.rules = {
            "task_request": self._handle_task_request,
            "status_query": self._handle_status_query,
            "payment_offer": self._handle_payment_offer,
        }

    def make_decision(self, message_type: str, context: Dict[str, Any]) -> str:
        """Make a decision based on message type and context."""
        handler = self.rules.get(message_type, self._default_decision)
        return handler(context)

    def _handle_task_request(self, context: Dict[str, Any]) -> str:
        """Decide on task requests."""
        task = context.get('task')
        if task == 'compute':
            return 'accept'
        elif task == 'data_retrieval':
            return 'delegate'
        else:
            return 'reject'

    def _handle_status_query(self, context: Dict[str, Any]) -> str:
        """Handle status queries."""
        return 'respond_status'

    def _handle_payment_offer(self, context: Dict[str, Any]) -> str:
        """Handle payment offers."""
        amount = context.get('amount', 0)
        if amount > 1000:  # Arbitrary threshold
            return 'accept_payment'
        else:
            return 'negotiate'

    def _default_decision(self, context: Dict[str, Any]) -> str:
        """Default decision for unknown types."""
        return 'acknowledge'