#!/usr/bin/env python3
import json
from typing import Dict, Any, Optional
from .a2a import A2A
from .lifecycle_manager import AgentLifecycleManager
from .identity_manager import AgentIdentityManager

class MessageHandler:
    def __init__(self, lifecycle_manager: AgentLifecycleManager, identity_manager: AgentIdentityManager, a2a: A2A):
        self.lifecycle_manager = lifecycle_manager
        self.identity_manager = identity_manager
        self.a2a = a2a

    def handle_incoming_message(self, envelope: Dict[str, Any]) -> Optional[str]:
        """Handle an incoming A2A envelope."""
        # Verify signature if present
        sender_id = envelope.get('from')
        if sender_id and 'sig' in envelope:
            sender_identity = self.identity_manager.get_identity(sender_id)
            if sender_identity and not A2A.verify(envelope, sender_identity['signature_public_key']):
                return "Invalid signature"

        # Decrypt body if encrypted
        body = envelope.get('body')
        if not body and 'body_enc' in envelope:
            # Assume we have the recipient's encryption key
            # In real implementation, get from identity
            recipient_enc_sk_b64 = ""  # Placeholder
            body = A2A.decrypt_body(envelope, recipient_enc_sk_b64)

        if not body:
            return "No body"

        # Route to appropriate agent
        to_agent_id = envelope.get('to')
        agent = self.lifecycle_manager.get_agent(to_agent_id)
        if not agent:
            return f"Agent {to_agent_id} not found"

        # Handle the message
        response = agent.handle_message(envelope)
        return response

    def send_message(self, from_agent_id: str, to_agent_id: str, message_type: str, body: Dict[str, Any],
                    recipient_onion: str, encrypt: bool = False) -> bool:
        """Send a message from one agent to another via A2A."""
        # Get sender identity
        sender_identity = self.identity_manager.get_identity(from_agent_id)
        if not sender_identity:
            return False

        # Get recipient identity for encryption
        recipient_identity = self.identity_manager.get_identity(to_agent_id)
        encrypt_pubkey = recipient_identity['encryption_public_key'] if recipient_identity and encrypt else None

        # Create envelope
        envelope = self.a2a.envelope(to_agent_id, message_type, body, from_agent_id, encrypt_pubkey)

        # Send via Tor
        return self.a2a.send_envelope(envelope, recipient_onion)