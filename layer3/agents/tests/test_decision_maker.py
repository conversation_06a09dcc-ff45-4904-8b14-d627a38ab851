#!/usr/bin/env python3
import unittest
from ..decision_maker import DecisionMaker

class TestDecisionMaker(unittest.TestCase):
    def setUp(self):
        self.dm = DecisionMaker()

    def test_task_request_decision(self):
        context = {'task': 'compute'}
        decision = self.dm.make_decision('task_request', context)
        self.assertEqual(decision, 'accept')

        context = {'task': 'data_retrieval'}
        decision = self.dm.make_decision('task_request', context)
        self.assertEqual(decision, 'delegate')

    def test_status_query_decision(self):
        decision = self.dm.make_decision('status_query', {})
        self.assertEqual(decision, 'respond_status')

    def test_payment_offer_decision(self):
        context = {'amount': 1500}
        decision = self.dm.make_decision('payment_offer', context)
        self.assertEqual(decision, 'accept_payment')

        context = {'amount': 500}
        decision = self.dm.make_decision('payment_offer', context)
        self.assertEqual(decision, 'negotiate')

    def test_default_decision(self):
        decision = self.dm.make_decision('unknown_type', {})
        self.assertEqual(decision, 'acknowledge')

if __name__ == '__main__':
    unittest.main()