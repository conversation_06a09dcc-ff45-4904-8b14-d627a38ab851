#!/usr/bin/env python3
import unittest
from ..a2a import A2A

class TestA2A(unittest.TestCase):
    def setUp(self):
        self.a2a = A2A()

    def test_envelope_creation(self):
        env = self.a2a.envelope("recipient", "test", {"msg": "hello"})
        self.assertEqual(env['to'], "recipient")
        self.assertEqual(env['type'], "test")
        self.assertEqual(env['body'], {"msg": "hello"})

    def test_verify_signature(self):
        # Without keys, should return False
        env = {"v": 1, "id": "test", "ts": 123, "from": "anon", "to": "rec", "type": "test", "body": {}}
        self.assertFalse(A2A.verify(env, "dummy_key"))

if __name__ == '__main__':
    unittest.main()