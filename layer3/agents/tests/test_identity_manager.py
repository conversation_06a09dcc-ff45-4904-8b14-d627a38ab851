#!/usr/bin/env python3
import unittest
from ..identity_manager import AgentIdentityManager

class TestAgentIdentityManager(unittest.TestCase):
    def setUp(self):
        self.im = AgentIdentityManager()

    def test_generate_identity(self):
        identity = self.im.generate_identity()
        self.assertTrue(self.im.validate_identity(identity))
        self.assertIn(identity['id'], self.im.identities)

    def test_load_identity(self):
        identity = self.im.generate_identity()
        im2 = AgentIdentityManager()
        self.assertTrue(im2.load_identity(identity))
        self.assertEqual(im2.get_identity(identity['id']), identity)

    def test_sign_and_verify(self):
        identity = self.im.generate_identity()
        agent_id = identity['id']
        message = b"test message"
        # Note: Need actual keys for signing, placeholder
        # signature = self.im.sign_message(agent_id, message, b"key")
        # self.assertTrue(self.im.verify_signature(agent_id, message, signature))

if __name__ == '__main__':
    unittest.main()