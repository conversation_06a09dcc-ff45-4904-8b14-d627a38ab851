#!/usr/bin/env python3
import argparse
import json
import os
import sys
import time
import requests

# Uses Layer 2 wallet RPC to pay and then retries the request with txid.
from layer2.chain.wallet_rpc_client import WalletRPC
from layer3.protocols.ap2 import build_ap2_auth
from layer3.agents.agent_runtime import get_planner


def get_via_tor(url: str, timeout: float = 15.0) -> requests.Response:
    proxies = {"http": "socks5h://127.0.0.1:9050", "https": "socks5h://127.0.0.1:9050"}
    return requests.get(url, proxies=proxies, timeout=timeout)


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--service', required=True, help='Service URL (onion or http) e.g., http://abc.onion/service')
    ap.add_argument('--wallet', default='http://127.0.0.1:18083', help='Wallet RPC endpoint')
    ap.add_argument('--rpc-user')
    ap.add_argument('--rpc-pass')
    ap.add_argument('--dest-address', help='Destination (override)')
    ap.add_argument('--mandate-id', help='Use AP2 mandate id for authorization if supported')
    ap.add_argument('--payer-sk', help='Path to payer Ed25519 secret key (for AP2)')
    args = ap.parse_args()

    r1 = get_via_tor(args.service)
    if r1.status_code != 402:
        print("Unexpected status:", r1.status_code, r1.text[:200])
        sys.exit(1)
    try:
        chall = r1.json()
    except Exception:
        print("402 did not contain JSON challenge")
        sys.exit(1)

    amount = int(chall.get('amount_atomic', '0'))
    address = args.dest_address or chall.get('address')
    if not address or amount <= 0:
        print("Invalid challenge")
        sys.exit(1)

    # Decide payment path (AP2 vs on-chain)
    planner = get_planner()
    method = planner.decide_payment(chall)

    headers = {}
    if method == 'ap2' and args.mandate_id and args.payer_sk:
        try:
            sk = open(args.payer_sk, 'rb').read()
            headers['X-AP2-Auth'] = build_ap2_auth(chall['invoice_id'], chall['nonce'], args.mandate_id, sk)
        except Exception as e:
            print('AP2 auth failed, falling back to on-chain:', e)
            method = 'onchain'

    if method == 'onchain':
        w = WalletRPC(args.wallet, args.rpc_user, args.rpc_pass, use_tor_socks=True)
        dests = [{"address": address, "amount": amount}]
        resp = w.transfer(dests)
        txid = resp.get('result', {}).get('tx_hash') or resp.get('result', {}).get('tx_hash_list', [''])[0]
        if not txid:
            print("No txid returned", resp)
            sys.exit(1)
        headers['X-402-TxId'] = txid

    proxies = {"http": "socks5h://127.0.0.1:9050", "https": "socks5h://127.0.0.1:9050"}
    r2 = requests.get(args.service, proxies=proxies, headers=headers, timeout=20)
    print("Status:", r2.status_code)
    print(r2.text[:1000])

if __name__ == '__main__':
    main()

