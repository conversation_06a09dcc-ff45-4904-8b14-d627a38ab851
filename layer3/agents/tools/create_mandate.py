#!/usr/bin/env python3
import argparse
import base64
import json
import os
import time
import uuid

try:
    from nacl import signing
except Exception:
    print("PyNaCl required for signing. Install with: pip install pynacl")
    raise


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--payer-sk', required=True, help='Path to payer Ed25519 secret key (32 bytes)')
    ap.add_argument('--payee-id', required=True)
    ap.add_argument('--asset', default='AN')
    ap.add_argument('--cap', required=True, type=int, help='Spending cap (atomic)')
    ap.add_argument('--endpoint', default='/service')
    ap.add_argument('--valid-days', type=int, default=365)
    ap.add_argument('--out', default='mandate.json')
    args = ap.parse_args()

    sk = signing.SigningKey(open(args.payer_sk, 'rb').read())
    vk = sk.verify_key
    payer_id = base64.b64encode(vk.encode()).decode()

    now = int(time.time())
    mandate = {
        "mandate_id": str(uuid.uuid4()),
        "payer_id": payer_id,
        "payee_id": args.payee_id,
        "asset": args.asset,
        "spending_cap": str(args.cap),
        "valid_from": time.strftime('%Y-%m-%dT%H:%M:%SZ', time.gmtime(now)),
        "valid_until": time.strftime('%Y-%m-%dT%H:%M:%SZ', time.gmtime(now + args.valid_days*86400)),
        "allowed_endpoints": [args.endpoint],
        "terms_cid": "",
    }
    msg = json.dumps(mandate, separators=(',', ':'), sort_keys=True).encode()
    sig = base64.b64encode(sk.sign(msg).signature).decode()
    mandate["payer_signature"] = sig

    with open(args.out, 'w') as f:
        json.dump(mandate, f, indent=2)
    print("Wrote", args.out)

if __name__ == '__main__':
    main()

