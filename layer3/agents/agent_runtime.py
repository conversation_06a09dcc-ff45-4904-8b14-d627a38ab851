#!/usr/bin/env python3
import os
from typing import Dict

# Optional LangChain/LangGraph integration. If not present, fallback to a simple rule-based planner.

def have_langdeps() -> bool:
    try:
        import langchain  # noqa: F401
        import langgraph  # noqa: F401
        return True
    except Exception:
        return False

class SimplePlanner:
    def decide_payment(self, challenge: Dict) -> str:
        """Return 'ap2' or 'onchain'. Uses env hint AGENTNET_USE_AP2."""
        if os.environ.get('AGENTNET_USE_AP2') == '1' and challenge.get('ap2_supported'):
            return 'ap2'
        return 'onchain'

# Placeholder for future LangGraph-based planner; currently returns same decision
class LangGraphPlanner(SimplePlanner):
    pass


def get_planner():
    if have_langdeps():
        return LangGraphPlanner()
    return SimplePlanner()

