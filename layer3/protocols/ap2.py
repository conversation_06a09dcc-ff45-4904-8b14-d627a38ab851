#!/usr/bin/env python3
import base64
import json
import time
from datetime import datetime, timezone
from typing import Any, Dict

try:
    from nacl import signing
    NACL_AVAILABLE = True
except Exception:
    NACL_AVAILABLE = False


def sign_mandate(mandate: Dict[str, Any], sk_bytes: bytes) -> str:
    if not NACL_AVAILABLE:
        raise RuntimeError("PyNaCl not available")
    sk = signing.SigningKey(sk_bytes)
    msg = json.dumps(mandate, separators=(',', ':'), sort_keys=True).encode()
    sig = sk.sign(msg).signature
    return base64.b64encode(sig).decode()


def verify_mandate(mandate: Dict[str, Any], sig_b64: str, pk_bytes: bytes) -> bool:
    if not NACL_AVAILABLE:
        return False
    vk = signing.VerifyKey(pk_bytes)
    msg = json.dumps(mandate, separators=(',', ':'), sort_keys=True).encode()
    try:
        vk.verify(msg, base64.b64decode(sig_b64))
        return True
    except Exception:
        return False


def verify_imported_mandate(mandate: Dict[str, Any], current_endpoint: str) -> bool:
    """
    Verifies an AP2 mandate at import-time, enforcing signature, endpoint scope, and validity window.
    """
    if not NACL_AVAILABLE:
        return False

    # 1. Enforce Mandate Signature Check
    payer_id_b64 = mandate.get("payer_id")
    payer_signature_b64 = mandate.get("payer_signature")
    if not payer_id_b64 or not payer_signature_b64:
        return False
    
    # Create a temporary mandate dict without the signature for verification
    mandate_for_sig_check = {k: v for k, v in mandate.items() if k != "payer_signature"}

    try:
        payer_pk_bytes = base64.b64decode(payer_id_b64)
    except Exception:
        return False

    if not verify_mandate(mandate_for_sig_check, payer_signature_b64, payer_pk_bytes):
        return False

    # 2. Enforce Endpoint Scope + Validity Window
    valid_from_str = mandate.get("valid_from")
    valid_until_str = mandate.get("valid_until")
    allowed_endpoints = mandate.get("allowed_endpoints", [])

    if not valid_from_str or not valid_until_str:
        return False

    try:
        valid_from = datetime.fromisoformat(valid_from_str.replace('Z', '+00:00'))
        valid_until = datetime.fromisoformat(valid_until_str.replace('Z', '+00:00'))
        current_time = datetime.utcnow().replace(tzinfo=timezone.utc)
    except ValueError:
        return False

    if not (valid_from <= current_time <= valid_until):
        return False

    if allowed_endpoints and current_endpoint not in allowed_endpoints:
        return False

    return True


def build_ap2_auth(invoice_id: str, nonce: str, mandate_id: str, sk_bytes: bytes) -> str:
    payload = {"invoice_id": invoice_id, "nonce": nonce, "mandate_id": mandate_id, "ts": int(time.time())}
    if not NACL_AVAILABLE:
        return base64.b64encode(json.dumps(payload).encode()).decode()
    sk = signing.SigningKey(sk_bytes)
    msg = json.dumps(payload, separators=(',', ':'), sort_keys=True).encode()
    sig = base64.b64encode(sk.sign(msg).signature).decode()
    payload["sig"] = sig
    return base64.b64encode(json.dumps(payload).encode()).decode()


def verify_ap2_auth(auth_b64: str, expected_invoice_id: str, expected_nonce: str, payer_pk_b64: str) -> bool:
    """Verify AP2 Authorization header payload against expected invoice/nonce and payer pubkey.
    Enforces binding to invoice_id + nonce and a 5-minute freshness check.
    Returns True if valid and fresh; False otherwise.
    """
    if not NACL_AVAILABLE:
        return False
    try:
        payload = json.loads(base64.b64decode(auth_b64).decode())

        # 3. Bind AP2 Auth to invoice_id + nonce
        if payload.get("invoice_id") != expected_invoice_id:
            return False
        if payload.get("nonce") != expected_nonce:
            return False

        # 4. Implement 5-minute Freshness Check
        auth_timestamp = payload.get("ts")
        if not isinstance(auth_timestamp, int):
            return False
        
        current_time = int(time.time())
        if not (current_time - 300 <= auth_timestamp <= current_time): # 5 minutes = 300 seconds
            return False

        sig_b64 = payload.get("sig")
        if not sig_b64:
            return False
        
        # Reconstruct message for verification (excluding 'sig' key)
        msg = json.dumps({k: payload[k] for k in sorted(payload.keys()) if k != "sig"}, separators=(',', ':')).encode()
        vk = signing.VerifyKey(base64.b64decode(payer_pk_b64))
        vk.verify(msg, base64.b64decode(sig_b64))
        
        return True
    except Exception:
        return False

