#!/usr/bin/env python3
import base64
import json
import os
import time
import uuid
import requests
from typing import Any, Dict, Optional

try:
    from nacl import signing
    from nacl.public import PrivateKey, PublicKey, Box
    NACL_AVAILABLE = True
except Exception:
    NACL_AVAILABLE = False


def agent_id_from_pubkey(pubkey_bytes: bytes) -> str:
    # Self-certifying ID: base64 of first 32 bytes hash (simple demo)
    import hashlib
    return base64.urlsafe_b64encode(hashlib.sha256(pubkey_bytes).digest()[:32]).decode().rstrip('=')


class A2A:
    def __init__(self, sk_path: Optional[str] = None, enc_sk_path: Optional[str] = None):
        self.signer = None
        self.pubkey = None
        self.enc_sk = None
        if sk_path and NACL_AVAILABLE and os.path.exists(sk_path):
            sk = signing.SigningKey(open(sk_path, 'rb').read())
            self.signer = sk
            self.pubkey = sk.verify_key.encode()
        if enc_sk_path and NACL_AVAILABLE and os.path.exists(enc_sk_path):
            self.enc_sk = PrivateKey(open(enc_sk_path, 'rb').read())

    def envelope(self, to: str, type_: str, body: Dict[str, Any], sender_id: Optional[str] = None,
                 encrypt_for_pubkey_b64: Optional[str] = None) -> Dict[str, Any]:
        env = {
            "v": 1,
            "id": str(uuid.uuid4()),
            "ts": int(time.time()),
            "from": sender_id or (agent_id_from_pubkey(self.pubkey) if self.pubkey else "anon"),
            "to": to,
            "type": type_,
        }
        if encrypt_for_pubkey_b64 and NACL_AVAILABLE and self.enc_sk:
            # Encrypt body with sender's enc_sk and recipient's enc_pk
            rec_pk = PublicKey(base64.b64decode(encrypt_for_pubkey_b64))
            box = Box(self.enc_sk, rec_pk)
            ct = box.encrypt(json.dumps(body).encode())
            env["body_enc"] = base64.b64encode(ct).decode()
            env["eph_pub"] = base64.b64encode(bytes(self.enc_sk.public_key)).decode()
        else:
            env["body"] = body
        if self.signer:
            msg = json.dumps(env, separators=(',', ':'), sort_keys=True).encode()
            sig = self.signer.sign(msg).signature
            env["sig"] = base64.b64encode(sig).decode()
        return env

    @staticmethod
    def decrypt_body(env: Dict[str, Any], recipient_enc_sk_b64: str) -> Optional[Dict[str, Any]]:
        if not NACL_AVAILABLE or "body_enc" not in env:
            return None
        try:
            sk = PrivateKey(base64.b64decode(recipient_enc_sk_b64))
            eph = PublicKey(base64.b64decode(env["eph_pub"]))
            box = Box(sk, eph)
            pt = box.decrypt(base64.b64decode(env["body_enc"]))
            return json.loads(pt.decode())
        except Exception:
            return None

    @staticmethod
    def verify(env: Dict[str, Any], pubkey_b64: str) -> bool:
        if not NACL_AVAILABLE or "sig" not in env:
            return False
        sig = base64.b64decode(env["sig"])  # type: ignore
        env2 = dict(env)
        env2.pop("sig", None)
        msg = json.dumps(env2, separators=(',', ':'), sort_keys=True).encode()
        try:
            vk = signing.VerifyKey(base64.b64decode(pubkey_b64))
            vk.verify(msg, sig)
            return True
        except Exception:
            return False

    def send_envelope(self, env: Dict[str, Any], recipient_onion: str, port: int = 80) -> bool:
        """Send an A2A envelope via Tor to a recipient's onion address."""
        try:
            proxies = {"http": "socks5h://127.0.0.1:9050", "https": "socks5h://127.0.0.1:9050"}
            url = f"http://{recipient_onion}:{port}/a2a"
            response = requests.post(url, json=env, proxies=proxies, timeout=30)
            return response.status_code == 200
        except Exception:
            return False

