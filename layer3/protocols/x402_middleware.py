#!/usr/bin/env python3
import json
import os
from http import HTTPStatus
from typing import Callable, Dict, Optional

# Minimal middleware helpers for 402 flows over http.server

class X402:
    def __init__(self, asset: str, amount_atomic: int, address: str):
        self.asset = asset
        self.amount = amount_atomic
        self.address = address

    def challenge(self, invoice_id: str, nonce: str, ap2_supported: bool = True):
        body = {
            "invoice_id": invoice_id,
            "asset": self.asset,
            "amount_atomic": str(self.amount),
            "address": self.address,
            "nonce": nonce,
            "ap2_supported": ap2_supported,
        }
        return 402, body

    def check_payment(self, headers: Dict[str, str]) -> Optional[str]:
        # Accept either an on-chain proof header or an AP2 auth header (demo)
        txid = headers.get('X-402-TxId')
        if txid:
            return txid
        ap2 = headers.get('X-AP2-Auth')
        if ap2:
            return ap2  # trust demo; production should verify
        return None

