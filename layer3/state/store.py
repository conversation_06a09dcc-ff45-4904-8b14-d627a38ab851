#!/usr/bin/env python3
import base64
import json
import os
import threading
import time
from typing import Any, Dict, Optional

from layer3.protocols.ap2 import verify_mandate

# Minimal JSON-backed store for mandates and usage accounting.
# Not safe for concurrent multi-process writers; sufficient for demo.
# File path via env AGENTNET_STATE_FILE (defaults to ./agent_state.json).

DEFAULT_PATH = os.environ.get("AGENTNET_STATE_FILE", os.path.abspath("agent_state.json"))

class JSONStore:
    def __init__(self, path: str = DEFAULT_PATH):
        self.path = path
        self._lock = threading.Lock()
        self._data: Dict[str, Any] = {"mandates": {}, "usage": {}}
        self._load()

    def _load(self):
        if os.path.exists(self.path):
            try:
                with open(self.path, 'r') as f:
                    self._data = json.load(f)
            except Exception:
                pass

    def _save(self):
        tmp = self.path + ".tmp"
        with open(tmp, 'w') as f:
            json.dump(self._data, f)
        os.replace(tmp, self.path)

    def _validate_mandate(self, m: Dict[str, Any]) -> bool:
        try:
            required = ["mandate_id", "payer_id", "payee_id", "asset", "spending_cap", "valid_from", "valid_until", "allowed_endpoints", "payer_signature"]
            for k in required:
                if k not in m:
                    return False
            pk_bytes = base64.b64decode(m["payer_id"])  # payer_id is base64(pubkey)
            sig_b64 = m["payer_signature"]
            return verify_mandate({k: m[k] for k in m if k != "payer_signature"}, sig_b64, pk_bytes)
        except Exception:
            return False

    # Mandates
    def import_mandates_file(self, mandates_path: str):
        with open(mandates_path, 'r') as f:
            mandates = json.load(f)
        if not isinstance(mandates, list):
            mandates = [mandates]
        with self._lock:
            for m in mandates:
                if not self._validate_mandate(m):
                    continue  # reject invalid
                mid = m["mandate_id"]
                self._data["mandates"][mid] = m
            self._save()

    def get_mandate(self, mandate_id: str) -> Optional[Dict[str, Any]]:
        return self._data.get("mandates", {}).get(mandate_id)

    # Usage accounting (atomic units)
    def get_consumed(self, mandate_id: str) -> int:
        return int(self._data.get("usage", {}).get(mandate_id, 0))

    def consume(self, mandate_id: str, amount_atomic: int) -> bool:
        with self._lock:
            m = self._data.get("mandates", {}).get(mandate_id)
            if not m or m.get("revoked"):
                return False
            cap = int(m.get("spending_cap", "0"))
            used = int(self._data.get("usage", {}).get(mandate_id, 0))
            if used + amount_atomic > cap:
                return False
            self._data.setdefault("usage", {})[mandate_id] = used + amount_atomic
            self._save()
            return True

