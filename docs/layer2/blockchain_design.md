# Blockchain Integration Design for Decentralized Database Layer

This document outlines the design for integrating a blockchain into the Decentralized Database Layer, focusing on immutable storage of agent transactions and identities while addressing anonymity, decentralization, scalability, and low transaction costs.

## 1. Choice of Blockchain: TON (The Open Network)

**Recommendation:** TON (The Open Network)

**Justification:**
TON is chosen for its robust architecture and features that align well with the project's requirements:

*   **Scalability:** TON's sharded architecture allows for high transaction throughput and parallel processing, making it highly scalable for a large number of agent transactions and micropayments.
*   **Low Transaction Costs:** TON is designed for fast and low-cost transactions, which is crucial for supporting frequent micropayments between agents.
*   **Decentralization:** Its sharded design promotes decentralization by distributing the network load across multiple chains.
*   **Anonymity Features:** TON's architecture includes built-in decentralized privacy tools, which are a significant advantage for maintaining agent anonymity.
*   **Maturity:** While continuously evolving, TON is a mature and actively developed blockchain with a growing ecosystem.

## 2. Data Structure

The data structure on the blockchain is designed to maintain anonymity while ensuring immutability and verifiability. Sensitive information will be stored off-chain, with only cryptographic hashes recorded on the blockchain.

### Agent Identities

*   **Public Key Hash**: A cryptographic hash of the agent's public key. This serves as a pseudonymous identifier on the blockchain, preventing direct linkage to real-world identities.
*   **Identity Hash (Optional)**: A hash of a more detailed, off-chain identity document (e.g., a self-sovereign identity credential). This allows for verifiable claims about an agent's identity without revealing the underlying data.
*   **Reputation Score Hash (Optional)**: A hash of an agent's aggregated reputation score, updated off-chain. This provides a verifiable, yet anonymous, measure of trustworthiness.

### Agent Transactions

*   **Transaction ID**: A unique identifier for each transaction.
*   **Sender Public Key Hash**: A hash of the sender's public key, ensuring pseudonymous attribution.
*   **Receiver Public Key Hash**: A hash of the receiver's public key.
*   **Amount**: The value transferred in the transaction (e.g., micropayment in Toncoin).
*   **Timestamp**: The time at which the transaction occurred.
*   **Content Hash (Optional)**: A cryptographic hash of any associated off-chain content (e.g., data exchanged, service rendered). This allows for verifiable linking to external data stored on IPFS.
*   **Mandate Hash (Optional)**: A hash of the mandate under which the transaction was executed, linking to off-chain mandate details.
*   **Signature**: The sender's digital signature, proving the authenticity and integrity of the transaction.

## 3. Interaction Mechanism

Agents will interact with the TON blockchain through a combination of direct transactions and smart contracts, facilitated by an RPC client.

*   **Agent Wallets**: Each agent will possess a TON wallet, managed by the `IdentityManager`, holding their public/private key pair and Toncoin balance.
*   **Direct Transactions**: For simple micropayments, agents will use direct Toncoin transfers between their wallets.
*   **Smart Contracts**:
    *   **Identity Contract**: A smart contract will manage agent identities, allowing agents to register their public key hashes and potentially hashes of off-chain identity claims.
    *   **Reputation Contract**: This contract will facilitate the recording of hashed attestations about agent performance, contributing to a verifiable and anonymous reputation system.
    *   **Transaction Logging Contract**: For more complex interactions, a smart contract will log transaction details such as `content_hash` and `mandate_hash`, providing an immutable record.
*   **Off-chain Data Storage (IPFS)**: Larger data payloads (e.g., full identity documents, detailed mandates) will be stored on IPFS, with only their cryptographic hashes recorded on the blockchain.
*   **RPC Client**: Agents will utilize an RPC client (e.g., `layer2/chain/rpc_client.py`, `layer2/chain/wallet_rpc_client.py`) to send transactions, interact with smart contracts, query blockchain state, and monitor events.

## 4. Anonymity Considerations

The blockchain design, in conjunction with the Anonymous Network Layer, aims to provide strong anonymity for agents.

*   **Pseudonymous Identities**: The use of public key hashes and ephemeral public keys (where agents generate new key pairs for transactions or periods) prevents direct linking of transactions to a persistent identity.
*   **Off-Chain Data**: All sensitive or personally identifiable information (PII) is kept off-chain on IPFS, with only cryptographic hashes stored on the blockchain. This ensures that the blockchain itself does not contain any directly identifiable data.
*   **Anonymous Network Layer Integration**:
    *   **Traffic Obfuscation**: The Anonymous Network Layer (e.g., Tor) will obfuscate network traffic, preventing the correlation of an agent's IP address with their blockchain activities.
    *   **Decoupling Identities**: The network-level identifiers are decoupled from blockchain-level pseudonyms, making it difficult to link an agent's online activity to their blockchain transactions.
    *   **Relay Transactions**: Agents can route blockchain transactions through anonymous relays or mixers provided by the Anonymous Network Layer for enhanced privacy.
*   **Zero-Knowledge Proofs (ZKP - Future)**: Future enhancements could include ZKPs to allow agents to prove certain attributes (e.g., possessing a minimum reputation score) without revealing the underlying data or their identity.
*   **Transaction Mixing (Future)**: Implementing CoinJoin-like mixing services via smart contracts could further obfuscate transaction origins and destinations for highly sensitive transfers.

This integrated approach ensures that while the blockchain provides an immutable and verifiable ledger, the overall system maintains a high degree of anonymity for agents operating within the Decentralized Database Layer.