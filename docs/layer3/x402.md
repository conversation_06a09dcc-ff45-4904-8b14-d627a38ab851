# X.402: HTTP Micropayments for Agents

A minimal 402 Payment Required flow for onion services, supporting either on-chain proof (txid) or AP2 mandate authorization.

## Server -> Client (Challenge)
- HTTP 402 with JSON body:
```
{
  "invoice_id": "uuid",
  "asset": "AN",
  "amount_atomic": "50000",
  "address": "agentnet_address",
  "nonce": "random",
  "ap2_supported": true
}
```
- Headers may include `X-402-Asset`, `X-402-Amount`, `X-402-Nonce`.

## Client -> Server (Response)
- On-chain: add header `X-402-TxId: <txid>` and retry request
- AP2: add header `X-AP2-Auth: <base64(json)>` containing mandate reference and signature over nonce+invoice

## Server behavior
- Verify txid via wallet/daemon RPC or verify AP2 authorization and quotas
- On success, return 200 with content; else return 402 again

## Security notes
- Use Tor HS exclusively; bind localhost
- Randomize nonce; expire invoices quickly; avoid reuse

