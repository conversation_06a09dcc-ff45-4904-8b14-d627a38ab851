# AP2: Agent Payments Protocol (Mandate-Based Authorization)

AP2 lets a payer grant a mandate to a payee to withdraw up to a spending cap, bounded by time and scope. Mandates are signed by the payer and can authorize off-chain service consumption with later on-chain settlement.

## Mandate (JSON)
- See docs/schemas/mandate.schema.json
- Fields: mandate_id, payer_id, payee_id, asset, spending_cap, validity window, allowed_endpoints, terms_cid, payer_signature.

## Lifecycle
1) Issue: Payer signs and transmits mandate to payee (A2A message)
2) Use: Client presents mandate (or reference) in an X.402 challenge response
3) Settle: Payee aggregates usage and triggers on-chain transfers (wallet RPC) with proof references
4) Revoke: Payer issues revocation (A2A) or mandate expires

## Security (implemented in reference server)
- Mandate signature is verified at import-time against `payer_id` (base64 Ed25519 pubkey)
- X-AP2-Auth payload is verified against invoice_id + nonce and payer pubkey
- Freshness enforced (default 5 minutes); nonces bound to issued invoices
- Endpoint scoping and validity window (valid_from/valid_until) enforced
- Spending cap enforced; usage accounted per mandate id

## Interop with X.402
- A 402 challenge includes `ap2_supported: true` and a `nonce`; client responds with `X-AP2-Auth` header containing mandate id and a payer signature over the nonce+invoice

