# Layer 3: Agent Economy & Protocols (A2A, AP2, X.402)

This layer defines the interaction protocols and sample implementations for autonomous agents to communicate and transact privately over Layer 1/2.

## Protocols
- A2A (Agent-to-Agent): signed, encrypted message envelopes for requests/replies and attestations; transport via HTTP over Tor HS (optionally WS later)
- AP2 (Agent Payments Protocol): mandate-based authorization for value transfers; used for preauthorization and settlement
- X.402 (HTTP Micropayments): 402 Payment Required challenge/response using AP2 or on-chain proof (txid)

## Components
- docs/layer3/a2a.md – message model and security properties
- docs/layer3/ap2.md – mandates, signatures, lifecycle (issue->use->revoke)
- docs/layer3/x402.md – HTTP flow, headers, and client/server behaviors
- layer3/protocols/ – reference Python implementations (no external services)
- layer3/agents/ – sample service and client agents demonstrating the flows

## Transport
- All endpoints are behind Tor HS (.onion); clients use Tor SOCKS (127.0.0.1:9050)
- Request bodies and A2A envelopes are end-to-end encrypted (recommended); the reference code includes signing and an encryption stub (enable NaCl if installed)

## Payment options
- On-chain proof (txid) using private chain wallet RPC (simplest demo)
- AP2-mandate-based off-chain authorization with periodic settlement (preferred at scale)
  - Reference server enforces: mandate signature at import, AP2 auth signature, nonce binding, freshness, endpoint scope, validity window, and spending caps

## Reputation & trust
- Attestations as signed A2A messages referencing content or transaction CIDs/txids
- No central registry; agents aggregate attestations for local decisions

## Dependencies
- Server code uses Python standard library
- Client code uses `requests` (and optionally `requests[socks]` for Tor proxy)
- Optional crypto signing uses PyNaCl if present; otherwise messages are unsigned (demo mode)

## Environment variables (agents)
- Service agent:
  - AGENTNET_SERVICE_PORT (default 8090)
  - AGENTNET_ASSET (default AN)
  - AGENTNET_PRICE_ATOMIC (default 50000)
  - AGENTNET_ADDRESS (chain receive address)
  - WALLET_RPC (default http://127.0.0.1:18083), RPC_USER, RPC_PASS (for tx verification)
  - AGENTNET_MANDATES_FILE (optional path to JSON mandates) and AGENTNET_PAYER_PUBKEY_B64 (fallback trust)
- Client agent:
  - AGENTNET_USE_AP2=1 to prefer AP2 when supported

