# Agent Economy & Application Layer Design

This document outlines the design for the Autonomous Agent Economy and Application Layer, focusing on the A2A (Agent-to-Agent) and AP2 (Agent Payments Protocol) protocols, the overall agent architecture, and the integration of an anonymous cryptocurrency.

## 1. A2A Protocol Design

The A2A protocol facilitates secure and anonymous communication between agents over the Anonymous Network Layer (Tor).

### Message Format

A signed (and optionally encrypted) JSON envelope is used for agent communication:

```json
{
  "v": 1,
  "id": "uuid",
  "ts": 1700000000,
  "from": "agent-id",
  "to": "agent-id",
  "type": "request|response|attestation|notice",
  "body": { /* application payload */ },
  "body_enc": "base64(encrypted_body)", // Optional: if encrypted
  "eph_pub": "base64(sender_ephemeral_pubkey)", // Optional: if encrypted
  "sig": "base64(ed25519_sig)"
}
```

*   `v`: Protocol version.
*   `id`: Unique message identifier (UUID) for replay protection.
*   `ts`: Timestamp for freshness checks.
*   `from`: Sender's agent ID (self-certifying hash of public key or explicit key label).
*   `to`: Recipient's agent ID.
*   `type`: Defines the message's purpose (e.g., request, response, attestation, notice).
*   `body`: The application-specific payload (plaintext if not encrypted).
*   `body_enc`: Base64 encoded encrypted `body`.
*   `eph_pub`: Sender's ephemeral X25519 public key, used for `body_enc`.
*   `sig`: Base64 encoded Ed25519 signature of the entire envelope (excluding `sig` itself) for authenticity and integrity.

### Agent Discovery

Agent discovery is assumed to occur through an out-of-band mechanism or a decentralized directory service, where agents can register and query for other agents' onion endpoints and public keys.

### Secure Communication Channels

*   **Anonymous Network Layer:** Communication is routed through Tor hidden services (onion endpoints), leveraging Counter-RAPTOR mitigations (guard selection, padding, jitter) for enhanced anonymity and resilience against network attacks.
*   **Encryption:** Optional end-to-end encryption is provided using X25519 + AEAD (PyNaCl Box). The sender encrypts the message body using the recipient's public encryption key and an ephemeral key for forward secrecy.
*   **Authentication:** Ed25519 digital signatures ensure message authenticity and integrity. The recipient verifies the signature using the sender's public signing key.

### Message Exchange Flow

1.  **Sender:** Constructs the A2A envelope, optionally encrypts the body, signs the envelope, and sends it via HTTP POST to the recipient's onion endpoint using Tor proxies.
2.  **Recipient:** Receives the envelope, verifies the signature, checks `id` and `ts` for replay protection, decrypts the body if necessary, and processes the message based on its `type`.

## 2. AP2 Protocol Design

The AP2 protocol enables agent payments, leveraging the X.402 protocol with stablecoins on the TON blockchain for instant, low-cost micropayments.

### Leveraging X.402

AP2 integrates with the HTTP 402 "Payment Required" flow. A service agent issues a 402 challenge with a JSON body containing `invoice_id`, `asset` (e.g., "AN" for the anonymous cryptocurrency), `amount_atomic`, `address`, `nonce`, and `ap2_supported: true`.

### Stablecoins on TON Blockchain

Payments are executed using stablecoins on The Open Network (TON) blockchain. TON is selected for its:
*   **Scalability:** Sharded architecture supports high transaction throughput.
*   **Low Transaction Costs:** Essential for frequent micropayments.
*   **Decentralization:** Distributed network load across multiple chains.
*   **Anonymity Features:** Built-in decentralized privacy tools.

### Mandate-Based Authorization

AP2 introduces "mandates" for off-chain service authorization and on-chain settlement. A mandate is a signed authorization from a payer to a payee, allowing withdrawals up to a spending cap, bounded by time and scope.

#### Mandate Structure (JSON)

```json
{
  "mandate_id": "uuid",
  "payer_id": "agent-id",
  "payee_id": "agent-id",
  "asset": "AN",
  "spending_cap": "1000000000", // atomic units
  "validity_window": {
    "from": 1700000000,
    "until": 1700000000
  },
  "allowed_endpoints": ["onion_address_1", "onion_address_2"],
  "terms_cid": "ipfs_cid_to_terms",
  "payer_signature": "base64(ed25519_sig)"
}
```

*   `mandate_id`: Unique identifier.
*   `payer_id`: Agent granting the mandate.
*   `payee_id`: Agent authorized to use the mandate.
*   `asset`: Cryptocurrency asset (e.g., stablecoin on TON).
*   `spending_cap`: Maximum authorized amount.
*   `validity_window`: Timeframe for mandate validity.
*   `allowed_endpoints`: Service endpoints where the mandate is valid.
*   `terms_cid`: IPFS Content Identifier for detailed terms and conditions.
*   `payer_signature`: Payer's digital signature over the mandate content.

### Payment Flow

1.  **Mandate Issue:** Payer creates and signs a mandate, then transmits it to the Payee via an A2A message.
2.  **Service Request & X.402 Challenge:** Client requests a service; if payment is needed, the Service Agent (Payee) issues an HTTP 402 challenge with `invoice_id` and `nonce`.
3.  **AP2 Authorization Response:** Client, with a valid mandate, constructs an `X-AP2-Auth` header containing a signed payload (invoice\_id, nonce, mandate\_id, payer\_signature) and retries the request.
4.  **Server-Side Verification:** Service Agent verifies the `X-AP2-Auth` header, including signature, freshness, mandate validity (spending cap, time, scope), and accounts for usage.
5.  **On-Chain Settlement:** Payee aggregates usage and periodically initiates on-chain stablecoin transfers from the Payer's TON wallet, referencing mandates and proofs for auditability.

### Transaction Types

*   **Mandate Issuance:** Off-chain, signed A2A message.
*   **Service Authorization:** Off-chain, signed `X-AP2-Auth` header.
*   **On-Chain Settlement:** Direct stablecoin transfers on TON, potentially via smart contracts.

### Integration with Blockchain

*   **Agent Wallets:** `AgentIdentityManager` manages TON wallet keys and addresses.
*   **Direct Transactions:** Simple stablecoin transfers.
*   **Smart Contracts:** Identity, Reputation, and Transaction Logging contracts on TON.
*   **RPC Client:** Agents use RPC clients to interact with TON for transactions, state queries, and event monitoring.
*   **Off-chain Data (IPFS):** Mandate terms and proofs stored on IPFS, with hashes on-chain.

## 3. Agent Architecture

The individual AI agent is a modular, stateful entity, leveraging a LangGraph-based decision-making process, integrating A2A and AP2 protocols, and interacting with the Decentralized Database Layer and Anonymous Network Layer.

### High-Level Agent Architecture

```mermaid
graph TD
    subgraph Agent
        A[Agent State] --> B(LangGraph)
        B -- "Receive Message" --> C(Message Handler)
        C -- "Process Message" --> D(Decision Maker)
        D -- "Execute Action" --> E(Tools / Internal Logic)
        E -- "Send Response" --> C
    end

    subgraph External Interactions
        C -- "A2A Protocol" --> F(Anonymous Network Layer)
        E -- "AP2 Protocol" --> G(TON Blockchain)
        E -- "Data Storage" --> H(IPFS)
    end

    subgraph Identity & Keys
        I[Agent Identity Manager] -- "Signing/Encryption Keys" --> C
        I -- "TON Wallet Address/Keys" --> G
    end

    F -- "Tor Network" --> F_ext[External Agents]
    G -- "Stablecoin Transactions" --> G_ext[External Wallets/Contracts]
    H -- "Content Storage/Retrieval" --> H_ext[External IPFS Nodes]

    style Agent fill:#f9f,stroke:#333,stroke-width:2px
    style External Interactions fill:#ccf,stroke:#333,stroke-width:2px
    style Identity & Keys fill:#cfc,stroke:#333,stroke-width:2px
```

### Core Components

*   **`BaseAgent`**: Foundation class with agent ID, state (`AgentState`), and LangGraph instance for operational flow.
*   **`AgentIdentityManager`**: Manages cryptographic keys (Ed25519 for signing, X25519 for encryption), self-certifying agent IDs, and TON blockchain wallet addresses/keys.
*   **`MessageHandler`**: Handles incoming A2A messages (verification, decryption, routing) and outgoing A2A messages (construction, signing, encryption, Tor transport).
*   **`DecisionMaker`**: Rule-based system (extensible to LLM-driven) for agent actions based on message type and context.
*   **`AgentLifecycleManager`**: Manages agent registration, starting, and stopping, running agents in separate threads.
*   **`AgentRuntime` / `Planner`**: Guides payment strategy decisions (AP2 vs. on-chain) based on X.402 challenges.

### Integration with Protocols and Layers

*   **A2A Protocol:** `MessageHandler` uses the `A2A` class for message handling; `AgentIdentityManager` provides keys.
*   **AP2 Protocol:** `AgentIdentityManager` manages TON wallet keys; `DecisionMaker` interprets payment offers; `Planner` guides payment choices; agents use RPC clients for TON blockchain interaction.
*   **Decentralized Database Layer (TON & IPFS):** `AgentIdentityManager` manages TON wallet keys; agents use RPC clients for TON transactions and smart contract interaction; IPFS stores larger data payloads (mandate terms, proofs) with hashes on TON.
*   **Anonymous Network Layer (Tor):** `A2A` protocol explicitly uses Tor proxies for all agent-to-agent communication, enhanced by Counter-RAPTOR mitigations.

### Use of Frameworks

`BaseAgent` leverages `langgraph.graph.StateGraph` for flexible, stateful agent behaviors, allowing integration of LangChain components (LLMs, tools) for sophisticated AI-driven agents.

## 4. Anonymous Cryptocurrency Integration

The custom anonymous cryptocurrency, similar to Monero, will be integrated into the AP2 protocol and agent wallets on the TON blockchain to ensure sender, recipient, and amount privacy.

### Key Privacy Features

*   **Ring Signatures (Sender Privacy):**
    *   When an agent sends a transaction, it uses a Ring Signature to sign on behalf of a group (ring) of possible signers, including itself.
    *   This makes it computationally infeasible to determine which member of the ring actually signed the transaction, thus obfuscating the true sender.
    *   Integration: Agent wallets will generate Ring Signatures for all on-chain transactions, including AP2 settlements and direct payments. The `AgentIdentityManager` will facilitate the creation of these signatures by managing the agent's private spending key and coordinating with other public keys in the ring.

*   **Stealth Addresses (Recipient Privacy):**
    *   For every transaction, the sender generates a unique, one-time Stealth Address for the recipient.
    *   This means that the recipient's public address never appears directly on the blockchain, preventing external observers from linking transactions to a persistent recipient identity.
    *   Integration: When an agent is to receive funds (e.g., from an AP2 settlement), the payer's wallet will generate a Stealth Address for that specific transaction. The `AgentIdentityManager` will manage the agent's private view key, allowing it to scan the blockchain for transactions sent to Stealth Addresses it can spend.

*   **RingCT (Confidential Transactions - Amount Privacy):**
    *   RingCT hides the transaction amount from external observers while still allowing network participants to verify that no new coins were created (i.e., inputs equal outputs).
    *   This is achieved using cryptographic commitments and range proofs.
    *   Integration: All on-chain transactions, including AP2 settlements, will utilize RingCT. Agent wallets will automatically apply RingCT to obscure transaction amounts, ensuring that only the sender and recipient know the exact value transferred.

### Integration into AP2 Protocol and Agent Wallets

1.  **Agent Wallets:**
    *   Each agent's wallet (managed by `AgentIdentityManager`) will be enhanced to support these privacy features.
    *   It will hold a private spending key (for Ring Signatures), a private view key (for scanning Stealth Addresses), and a public address (for generating Stealth Addresses).
    *   The wallet will abstract away the complexity of these cryptographic operations, presenting a simple interface for sending and receiving anonymous cryptocurrency.

2.  **AP2 Protocol:**
    *   **On-Chain Settlement:** When an AP2 mandate is settled on-chain, the resulting stablecoin transaction will incorporate Ring Signatures (for sender privacy), Stealth Addresses (for recipient privacy), and RingCT (for amount privacy).
    *   **Mandate Details:** While the mandate itself (off-chain) will specify the `asset` and `spending_cap`, the actual on-chain settlement will leverage these privacy features to obscure the transaction details on the public ledger. The `asset` will refer to the anonymous stablecoin.

### Ensuring Privacy

*   **Sender Privacy:** Ring Signatures make it impossible to link a transaction to a specific sending agent within a group.
*   **Recipient Privacy:** Stealth Addresses ensure that the recipient's persistent identity is never exposed on the blockchain, preventing transaction graph analysis.
*   **Amount Privacy:** RingCT hides the transaction value, preventing observers from knowing how much was transferred.

This comprehensive integration ensures that the agent economy operates with a high degree of financial privacy, crucial for maintaining anonymity and preventing surveillance within the decentralized agent network.