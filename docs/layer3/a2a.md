# A2A: Agent-to-Agent Messaging

A signed (and optionally encrypted) JSON envelope for agent communication over HTTP on onion endpoints.

## Envelope
```
{
  "v": 1,
  "id": "uuid",
  "ts": 1700000000,
  "from": "agent-id",
  "to": "agent-id",
  "type": "request|response|attestation|notice",
  "body": { /* application payload */ },
  "sig": "base64(ed25519_sig)"  // optional in demo mode
}
```

- `agent-id` is self-certifying (hash of pubkey) or an explicit key label.
- The `sig` covers all fields except `sig`.

## Crypto
- Signing key: Ed25519 (PyNaCl) recommended.
- Encryption: X25519 + AEAD via PyNaCl Box (optional). Reference supports encrypting body to recipient pubkey; fields: `body_enc` (base64), `eph_pub` (sender ephemeral enc pubkey). Backwards-compatible: if not set, `body` is plaintext.

## Transport
- POST /a2a to the recipient onion endpoint.

## Replay and ordering
- Include `id` and `ts`; receivers should reject duplicate ids and expired timestamps.

